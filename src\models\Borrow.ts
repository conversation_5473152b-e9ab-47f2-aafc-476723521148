// src/models/Borrow.ts
import mongoose, { Document, Schema, Types } from 'mongoose';

export interface IBorrow extends Document {
  book: Types.ObjectId;
  quantity: number;
  borrowDate: Date;
  dueDate: Date;
}

const BorrowSchema: Schema = new Schema({
  book: { type: Schema.Types.ObjectId, ref: 'Book', required: true },
  quantity: { type: Number, required: true, min: 1 },
  borrowDate: { type: Date, default: Date.now },
  dueDate: { type: Date, required: true },
}, { timestamps: true });

export default mongoose.model<IBorrow>('Borrow', BorrowSchema);