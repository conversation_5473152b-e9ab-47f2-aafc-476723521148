// src/controllers/borrowController.ts
import { Request, Response } from 'express';
import Book from '../models/Book';
import Borrow from '../models/Borrow';
import mongoose from 'mongoose';

// @desc    Borrow a book
// @route   POST /api/borrows
// @access  Public
export const borrowBook = async (req: Request, res: Response) => {
  const { bookId, quantity, dueDate } = req.body;

  if (!bookId || !quantity || !dueDate) {
    return res.status(400).json({ message: 'Please provide bookId, quantity, and dueDate' });
  }
  if (quantity <= 0) {
    return res.status(400).json({ message: 'Quantity must be at least 1' });
  }
  if (!mongoose.Types.ObjectId.isValid(bookId)) {
    return res.status(400).json({ message: 'Invalid Book ID' });
  }

  try {
    const book = await Book.findById(bookId);
    if (!book) {
      return res.status(404).json({ message: 'Book not found' });
    }

    if (book.availableCopies < quantity) {
      return res.status(400).json({ message: `Not enough copies available. Only ${book.availableCopies} left.` });
    }

    book.availableCopies -= quantity;
    book.isAvailable = book.availableCopies > 0;
    await book.save();

    const newBorrow = new Borrow({
      book: bookId,
      quantity,
      dueDate,
    });

    const createdBorrow = await newBorrow.save();
    res.status(201).json({ message: 'Book borrowed successfully', borrow: createdBorrow });
  } catch (err: any) {
    res.status(500).json({ message: err.message });
  }
};

// @desc    Get borrow summary
// @route   GET /api/borrows/summary
// @access  Public
export const getBorrowSummary = async (req: Request, res: Response) => {
  try {
    const summary = await Borrow.aggregate([
      {
        $group: {
          _id: '$book',
          totalQuantityBorrowed: { $sum: '$quantity' },
        },
      },
      {
        $lookup: {
          from: 'books', // The collection name in MongoDB (usually plural of model name)
          localField: '_id',
          foreignField: '_id',
          as: 'bookDetails',
        },
      },
      {
        $unwind: '$bookDetails', // Deconstructs the array
      },
      {
        $project: {
          _id: 0, // Exclude _id from the final output
          bookId: '$_id',
          bookTitle: '$bookDetails.title',
          bookISBN: '$bookDetails.isbn',
          totalQuantityBorrowed: 1,
        },
      },
    ]);
    res.json(summary);
  } catch (err: any) {
    res.status(500).json({ message: err.message });
  }
};