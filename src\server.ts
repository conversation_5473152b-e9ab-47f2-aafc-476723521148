// src/server.ts
import express from 'express';
import dotenv from 'dotenv';
import cors from 'cors';
import connectDB from './config/db';
import bookRoutes from './routes/bookRoutes';
import borrowRoutes from './routes/borrowRoutes';

dotenv.config();

const app = express();

// Connect Database
connectDB();

// Init Middleware
app.use(express.json()); // For parsing application/json
app.use(cors()); // Enable CORS for all origins

// Define Routes
app.use('/api/books', bookRoutes);
app.use('/api/borrows', borrowRoutes);

// Basic route for testing
app.get('/', (req, res) => res.send('API is running...'));

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => console.log(`Server running on port ${PORT}`));