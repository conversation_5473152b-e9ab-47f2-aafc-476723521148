// src/routes/bookRoutes.ts
import { Router } from 'express';
import {
  getBooks,
  getBookById,
  createBook,
  updateBook,
  deleteBook,
} from '../controllers/bookController';

const router = Router();

router.get('/', getBooks);
router.get('/:id', getBookById);
router.post('/', createBook);
router.patch('/:id', updateBook); // Use PATCH for partial updates
router.delete('/:id', deleteBook);

export default router;