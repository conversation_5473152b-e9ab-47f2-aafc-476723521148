// src/models/Book.ts
import mongoose, { Document, Schema } from 'mongoose';

export interface IBook extends Document {
  title: string;
  author: string;
  genre: string;
  isbn: string;
  description: string;
  copies: number;
  availableCopies: number;
  isAvailable: boolean;
}

const BookSchema: Schema = new Schema({
  title: { type: String, required: true },
  author: { type: String, required: true },
  genre: { type: String, required: true },
  isbn: { type: String, required: true, unique: true },
  description: { type: String },
  copies: { type: Number, required: true, min: 0 },
  availableCopies: { type: Number, required: true, min: 0 },
  isAvailable: { type: Boolean, default: true },
}, { timestamps: true });

// Pre-save hook to update isAvailable based on availableCopies
BookSchema.pre<IBook>('save', function(next) {
  this.isAvailable = this.availableCopies > 0;
  next();
});

// Virtual for availability (optional, can be derived on frontend or in pre-save)
BookSchema.virtual('availability').get(function() {
  return this.availableCopies > 0 ? 'Available' : 'Unavailable';
});


export default mongoose.model<IBook>('Book', BookSchema);