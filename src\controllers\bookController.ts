// src/controllers/bookController.ts
import { Request, Response } from 'express';
import Book, { IBook } from '../models/Book';
import mongoose from 'mongoose';

// @desc    Get all books
// @route   GET /api/books
// @access  Public
export const getBooks = async (req: Request, res: Response) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;

    const books = await Book.find().skip(skip).limit(limit);
    const totalBooks = await Book.countDocuments();

    res.json({
      data: books,
      currentPage: page,
      totalPages: Math.ceil(totalBooks / limit),
      totalBooks,
    });
  } catch (err: any) {
    res.status(500).json({ message: err.message });
  }
};

// @desc    Get single book by ID
// @route   GET /api/books/:id
// @access  Public
export const getBookById = async (req: Request, res: Response) => {
  try {
    if (!mongoose.Types.ObjectId.isValid(req.params.id)) {
      return res.status(400).json({ message: 'Invalid Book ID' });
    }
    const book = await Book.findById(req.params.id);
    if (!book) {
      return res.status(404).json({ message: 'Book not found' });
    }
    res.json(book);
  } catch (err: any) {
    res.status(500).json({ message: err.message });
  }
};

// @desc    Create a new book
// @route   POST /api/books
// @access  Public
export const createBook = async (req: Request, res: Response) => {
  const { title, author, genre, isbn, description, copies } = req.body;

  if (!title || !author || !genre || !isbn || copies === undefined) {
    return res.status(400).json({ message: 'Please enter all required fields: title, author, genre, isbn, copies' });
  }

  try {
    const existingBook = await Book.findOne({ isbn });
    if (existingBook) {
      return res.status(400).json({ message: 'A book with this ISBN already exists.' });
    }

    const newBook: IBook = new Book({
      title,
      author,
      genre,
      isbn,
      description,
      copies,
      availableCopies: copies, // Initially available copies = total copies
      isAvailable: copies > 0,
    });

    const createdBook = await newBook.save();
    res.status(201).json(createdBook);
  } catch (err: any) {
    res.status(500).json({ message: err.message });
  }
};

// @desc    Update a book
// @route   PATCH /api/books/:id
// @access  Public
export const updateBook = async (req: Request, res: Response) => {
  const { title, author, genre, isbn, description, copies } = req.body;

  try {
    if (!mongoose.Types.ObjectId.isValid(req.params.id)) {
      return res.status(400).json({ message: 'Invalid Book ID' });
    }

    const book = await Book.findById(req.params.id);
    if (!book) {
      return res.status(404).json({ message: 'Book not found' });
    }

    // Handle ISBN change (if provided and different from current)
    if (isbn && isbn !== book.isbn) {
      const existingBookWithNewIsbn = await Book.findOne({ isbn });
      if (existingBookWithNewIsbn && existingBookWithNewIsbn._id.toString() !== book._id.toString()) {
        return res.status(400).json({ message: 'Another book with this ISBN already exists.' });
      }
    }

    // If copies are updated, recalculate availableCopies
    if (copies !== undefined && copies !== book.copies) {
      // You need to decide how to handle this.
      // For simplicity, let's say if total copies increase, available copies increase by the same amount.
      // If total copies decrease, available copies decrease, but not below 0 or below currently borrowed count.
      const borrowedCount = book.copies - book.availableCopies;
      if (copies < borrowedCount) {
         return res.status(400).json({ message: `Cannot reduce total copies below currently borrowed copies (${borrowedCount}).` });
      }
      book.availableCopies = copies - borrowedCount; // Recalculate
      book.copies = copies;
    }

    // Update other fields
    book.title = title ?? book.title;
    book.author = author ?? book.author;
    book.genre = genre ?? book.genre;
    book.isbn = isbn ?? book.isbn;
    book.description = description ?? book.description;

    const updatedBook = await book.save();
    res.json(updatedBook);
  } catch (err: any) {
    res.status(500).json({ message: err.message });
  }
};


// @desc    Delete a book
// @route   DELETE /api/books/:id
// @access  Public
export const deleteBook = async (req: Request, res: Response) => {
  try {
    if (!mongoose.Types.ObjectId.isValid(req.params.id)) {
      return res.status(400).json({ message: 'Invalid Book ID' });
    }
    const book = await Book.findByIdAndDelete(req.params.id);
    if (!book) {
      return res.status(404).json({ message: 'Book not found' });
    }
    res.json({ message: 'Book removed' });
  } catch (err: any) {
    res.status(500).json({ message: err.message });
  }
};